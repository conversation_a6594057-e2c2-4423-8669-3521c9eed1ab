import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId]/available-cranes - Get cranes available for assignment to a project
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for available cranes GET API");

    const { projectId } = await params;
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    let availableCranes;

    if (startDate && endDate) {
      // Get cranes that are available during the specified time period
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      availableCranes = await db.execute(sql`
        SELECT DISTINCT 
          c.id,
          c.name,
          c.model,
          c.type,
          c.capacity,
          c.height,
          c.reach,
          c.status,
          c.last_maintenance_date,
          c.next_maintenance_date,
          c.created_at,
          c.updated_at,
          CASE 
            WHEN EXISTS (
              SELECT 1 FROM bookings b2 
              WHERE b2.crane_id = c.id 
                AND b2.project_id = ${projectId}
                AND b2.deleted_at IS NULL
            ) THEN true 
            ELSE false 
          END as already_assigned
        FROM cranes c
        WHERE c.deleted_at IS NULL
          AND c.status = 'available'
          AND NOT EXISTS (
            SELECT 1 FROM bookings b
            WHERE b.crane_id = c.id
              AND b.deleted_at IS NULL
              AND (
                (b.start_date BETWEEN ${startDateObj} AND ${endDateObj})
                OR (b.end_date BETWEEN ${startDateObj} AND ${endDateObj})
                OR (${startDateObj} BETWEEN b.start_date AND b.end_date)
                OR (${endDateObj} BETWEEN b.start_date AND b.end_date)
              )
          )
        ORDER BY c.name
      `);
    } else {
      // Get all available cranes without time constraints
      availableCranes = await db.execute(sql`
        SELECT DISTINCT 
          c.id,
          c.name,
          c.model,
          c.type,
          c.capacity,
          c.height,
          c.reach,
          c.status,
          c.last_maintenance_date,
          c.next_maintenance_date,
          c.created_at,
          c.updated_at,
          CASE 
            WHEN EXISTS (
              SELECT 1 FROM bookings b2 
              WHERE b2.crane_id = c.id 
                AND b2.project_id = ${projectId}
                AND b2.deleted_at IS NULL
            ) THEN true 
            ELSE false 
          END as already_assigned
        FROM cranes c
        WHERE c.deleted_at IS NULL
          AND c.status = 'available'
        ORDER BY c.name
      `);
    }

    return NextResponse.json({ 
      cranes: availableCranes,
      filters: {
        startDate,
        endDate,
        projectId
      }
    });
  } catch (error) {
    console.error("[AVAILABLE_CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
