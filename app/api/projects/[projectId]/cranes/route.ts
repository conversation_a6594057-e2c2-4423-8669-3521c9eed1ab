import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId]/cranes - Get cranes assigned to a project
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project cranes GET API");

    const { projectId } = await params;

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Get cranes that have bookings for this project
    const projectCranes = await db.execute(sql`
      SELECT DISTINCT 
        c.id,
        c.name,
        c.model,
        c.type,
        c.capacity,
        c.height,
        c.reach,
        c.status,
        c.last_maintenance_date,
        c.next_maintenance_date,
        c.created_at,
        c.updated_at,
        COUNT(b.id) as booking_count,
        MIN(b.start_date) as next_booking_start,
        MAX(b.end_date) as last_booking_end
      FROM cranes c
      LEFT JOIN bookings b ON c.id = b.crane_id 
        AND b.project_id = ${projectId} 
        AND b.deleted_at IS NULL
      WHERE c.deleted_at IS NULL
        AND (b.project_id = ${projectId} OR b.project_id IS NULL)
      GROUP BY c.id, c.name, c.model, c.type, c.capacity, c.height, c.reach, 
               c.status, c.last_maintenance_date, c.next_maintenance_date, 
               c.created_at, c.updated_at
      HAVING COUNT(b.id) > 0
      ORDER BY c.name
    `);

    return NextResponse.json({ cranes: projectCranes });
  } catch (error) {
    console.error("[PROJECT_CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/projects/[projectId]/cranes - Assign a crane to a project (create booking)
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { projectId } = await params;
    const body = await req.json();
    const { craneId, startDate, endDate, notes } = body;

    if (!craneId) {
      return new NextResponse("Crane ID is required", { status: 400 });
    }

    if (!startDate || !endDate) {
      return new NextResponse("Start and end dates are required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Verify the crane exists
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Check for booking conflicts
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    const conflictingBookings = await db.execute(sql`
      SELECT * FROM bookings 
      WHERE crane_id = ${craneId}
      AND deleted_at IS NULL
      AND (
        (start_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (end_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (${startDateObj} BETWEEN start_date AND end_date)
        OR (${endDateObj} BETWEEN start_date AND end_date)
      )
    `);

    if (conflictingBookings.length > 0) {
      return new NextResponse("Booking conflicts with existing bookings", { status: 409 });
    }

    // Create the booking
    const newBooking = await db.insert(bookings).values({
      craneId,
      projectId,
      startDate: startDateObj,
      endDate: endDateObj,
      createdById: dbUser.id,
      notes: notes || null,
      status: "scheduled",
    }).returning();

    return NextResponse.json({ booking: newBooking[0] });
  } catch (error) {
    console.error("[PROJECT_CRANES_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
