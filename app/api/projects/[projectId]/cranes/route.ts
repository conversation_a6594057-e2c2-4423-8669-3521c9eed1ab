import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId]/cranes - Get cranes assigned to a project
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project cranes GET API");

    const { projectId } = await params;

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // For now, return an empty array since we haven't implemented bookings yet
    // We'll implement this functionality later
    const projectCranes: any[] = [];

    return NextResponse.json({ cranes: projectCranes });
  } catch (error) {
    console.error("[PROJECT_CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/projects/[projectId]/cranes - Assign a crane to a project (create booking)
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { projectId } = await params;
    const body = await req.json();
    const { craneId, startDate, endDate, notes } = body;

    if (!craneId) {
      return new NextResponse("Crane ID is required", { status: 400 });
    }

    if (!startDate || !endDate) {
      return new NextResponse("Start and end dates are required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Verify the crane exists
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Check for booking conflicts
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    const conflictingBookings = await db.execute(sql`
      SELECT * FROM bookings 
      WHERE crane_id = ${craneId}
      AND deleted_at IS NULL
      AND (
        (start_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (end_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (${startDateObj} BETWEEN start_date AND end_date)
        OR (${endDateObj} BETWEEN start_date AND end_date)
      )
    `);

    if (conflictingBookings.length > 0) {
      return new NextResponse("Booking conflicts with existing bookings", { status: 409 });
    }

    // Create the booking
    const newBooking = await db.insert(bookings).values({
      craneId,
      projectId,
      startDate: startDateObj,
      endDate: endDateObj,
      createdById: dbUser.id,
      notes: notes || null,
      status: "scheduled",
    }).returning();

    return NextResponse.json({ booking: newBooking[0] });
  } catch (error) {
    console.error("[PROJECT_CRANES_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
